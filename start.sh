#!/bin/bash

#=============================================================================
# ContiNew Admin 精简启动脚本
# 支持环境变量配置和基本进程管理
#=============================================================================

# 基础配置 - 可以在这里修改路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JAR_NAME="continew-admin.jar"
PID_FILE="${SCRIPT_DIR}/continew-admin.pid"
ENV_FILE="${SCRIPT_DIR}/.env"

# 默认路径配置（可以通过环境变量覆盖）
JAVA_HOME="${JAVA_HOME:-}"
APP_HOME="${APP_HOME:-${SCRIPT_DIR}}"
JAR_PATH="${JAR_PATH:-${APP_HOME}/app/bin/${JAR_NAME}}"

# 检查Java环境
check_java() {
    # 优先使用JAVA_HOME中的java
    if [ -n "${JAVA_HOME}" ] && [ -x "${JAVA_HOME}/bin/java" ]; then
        JAVA_CMD="${JAVA_HOME}/bin/java"
        echo "使用JAVA_HOME中的Java: ${JAVA_CMD}"
    elif command -v java &> /dev/null; then
        JAVA_CMD="java"
        echo "使用系统PATH中的Java: $(which java)"
    else
        echo "错误: 找不到Java环境"
        echo "请设置JAVA_HOME环境变量或确保java在PATH中"
        exit 1
    fi

    # 显示Java版本
    echo "Java版本: $(${JAVA_CMD} -version 2>&1 | head -n1)"
}

# 检查应用文件
check_app() {
    echo "检查应用文件: ${JAR_PATH}"
    if [ ! -f "${JAR_PATH}" ]; then
        echo "错误: 找不到应用JAR文件: ${JAR_PATH}"
        echo "请检查以下路径配置："
        echo "  APP_HOME: ${APP_HOME}"
        echo "  JAR_PATH: ${JAR_PATH}"
        exit 1
    fi
    echo "应用文件检查通过"
}

# 加载环境变量
load_environment() {
    # 设置默认值
    export SPRING_PROFILES_ACTIVE="${SPRING_PROFILES_ACTIVE:-prod}"

    # 加载环境变量文件
    if [ -f "${ENV_FILE}" ]; then
        echo "加载环境变量文件: ${ENV_FILE}"

        # 处理分号分隔的环境变量格式: REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过空行和注释
            [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue

            # 处理分号分隔的多个变量
            IFS=';' read -ra VARS <<< "$line"
            for var in "${VARS[@]}"; do
                # 去除前后空格
                var=$(echo "$var" | xargs)
                if [[ "$var" =~ ^[A-Za-z_][A-Za-z0-9_]*= ]]; then
                    export "$var"
                fi
            done
        done < "${ENV_FILE}"
    else
        echo "警告: 环境变量文件不存在: ${ENV_FILE}"
    fi
}

# 检查进程是否运行
is_running() {
    if [ -f "${PID_FILE}" ]; then
        local pid=$(cat "${PID_FILE}")
        if ps -p "${pid}" > /dev/null 2>&1; then
            return 0
        else
            rm -f "${PID_FILE}"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start_app() {
    if is_running; then
        echo "应用已经在运行中，PID: $(cat ${PID_FILE})"
        return 1
    fi

    echo "正在启动应用..."

    # 获取JAR文件所在目录
    JAR_DIR="$(dirname "${JAR_PATH}")"
    cd "${JAR_DIR}" || {
        echo "错误: 无法切换到JAR目录: ${JAR_DIR}"
        exit 1
    }

    # 启动应用
    nohup ${JAVA_CMD} -jar "$(basename "${JAR_PATH}")" --spring.profiles.active=${SPRING_PROFILES_ACTIVE} > /dev/null 2>&1 &
    local pid=$!

    # 保存PID
    echo "${pid}" > "${PID_FILE}"

    # 等待启动
    sleep 3

    if is_running; then
        echo "应用启动成功，PID: ${pid}"
        return 0
    else
        echo "应用启动失败"
        return 1
    fi
}

# 停止应用
stop_app() {
    if ! is_running; then
        echo "应用未运行"
        return 1
    fi

    local pid=$(cat "${PID_FILE}")
    echo "正在停止应用，PID: ${pid}"

    # 停止进程
    kill "${pid}"

    # 等待进程结束
    local count=0
    while [ ${count} -lt 10 ] && ps -p "${pid}" > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
    done

    if ps -p "${pid}" > /dev/null 2>&1; then
        echo "强制终止进程"
        kill -9 "${pid}"
    fi

    rm -f "${PID_FILE}"
    echo "应用已停止"
}

# 查看状态
status_app() {
    if is_running; then
        local pid=$(cat "${PID_FILE}")
        echo "应用正在运行，PID: ${pid}"
    else
        echo "应用未运行"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动应用"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  status  - 查看运行状态"
    echo ""
    echo "环境变量配置文件: ${ENV_FILE}"
    echo "支持格式: REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7"
}

# 主函数
main() {
    check_java
    check_app
    load_environment

    case "${1:-help}" in
        start)
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            stop_app
            sleep 2
            start_app
            ;;
        status)
            status_app
            ;;
        *)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
