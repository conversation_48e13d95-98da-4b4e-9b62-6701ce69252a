#!/bin/bash

#=============================================================================
# ContiNew Admin 启动脚本
# 支持环境变量配置、进程管理、日志记录等企业级功能
# 作者: 子豪的专属脚本
# 版本: 1.0.0
#=============================================================================

# 脚本配置
SCRIPT_NAME="ContiNew Admin"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="continew-admin"
JAR_NAME="continew-admin.jar"
PID_FILE="${SCRIPT_DIR}/${APP_NAME}.pid"
LOG_FILE="${SCRIPT_DIR}/logs/startup.log"
ENV_FILE="${SCRIPT_DIR}/.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "${LOG_FILE}"
}

# 创建必要目录
create_directories() {
    mkdir -p "${SCRIPT_DIR}/logs"
    mkdir -p "${SCRIPT_DIR}/data/file"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或不在 PATH 中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "${JAVA_VERSION}" -lt 17 ]; then
        log_error "需要 Java 17 或更高版本，当前版本: ${JAVA_VERSION}"
        exit 1
    fi
    
    log_info "Java 环境检查通过，版本: $(java -version 2>&1 | head -n1)"
}

# 检查应用目录结构
check_app_structure() {
    local required_dirs=("bin" "lib" "config")
    local missing_dirs=()
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "${SCRIPT_DIR}/app/${dir}" ]; then
            missing_dirs+=("${dir}")
        fi
    done
    
    if [ ${#missing_dirs[@]} -ne 0 ]; then
        log_error "缺少必要目录: ${missing_dirs[*]}"
        log_error "请确保已正确执行 mvn clean package"
        exit 1
    fi
    
    if [ ! -f "${SCRIPT_DIR}/app/bin/${JAR_NAME}" ]; then
        log_error "找不到应用JAR文件: ${SCRIPT_DIR}/app/bin/${JAR_NAME}"
        exit 1
    fi
    
    log_info "应用目录结构检查通过"
}

# 加载环境变量
load_environment() {
    # 默认环境变量
    export SPRING_PROFILES_ACTIVE="${SPRING_PROFILES_ACTIVE:-prod}"
    export SERVER_PORT="${SERVER_PORT:-18000}"
    export DB_HOST="${DB_HOST:-127.0.0.1}"
    export DB_PORT="${DB_PORT:-3306}"
    export DB_NAME="${DB_NAME:-continew_admin}"
    export DB_USER="${DB_USER:-root}"
    export DB_PWD="${DB_PWD:-123456}"
    export REDIS_HOST="${REDIS_HOST:-127.0.0.1}"
    export REDIS_PORT="${REDIS_PORT:-6379}"
    export REDIS_PWD="${REDIS_PWD:-}"
    export REDIS_DB="${REDIS_DB:-0}"
    
    # 加载环境变量文件
    if [ -f "${ENV_FILE}" ]; then
        log_info "加载环境变量文件: ${ENV_FILE}"
        
        # 处理分号分隔的环境变量格式: REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过空行和注释
            [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue
            
            # 处理分号分隔的多个变量
            IFS=';' read -ra VARS <<< "$line"
            for var in "${VARS[@]}"; do
                # 去除前后空格
                var=$(echo "$var" | xargs)
                if [[ "$var" =~ ^[A-Za-z_][A-Za-z0-9_]*= ]]; then
                    export "$var"
                    var_name=$(echo "$var" | cut -d'=' -f1)
                    var_value=$(echo "$var" | cut -d'=' -f2-)
                    log_debug "设置环境变量: ${var_name}=${var_value}"
                fi
            done
        done < "${ENV_FILE}"
    else
        log_warn "环境变量文件不存在: ${ENV_FILE}，使用默认配置"
    fi
    
    log_info "环境变量加载完成"
}

# 构建JVM参数
build_jvm_args() {
    # 基础JVM参数
    JVM_ARGS="-server"
    
    # 内存配置
    JVM_ARGS="${JVM_ARGS} -Xms${JVM_XMS:-512m}"
    JVM_ARGS="${JVM_ARGS} -Xmx${JVM_XMX:-2g}"
    JVM_ARGS="${JVM_ARGS} -Xmn${JVM_XMN:-256m}"
    
    # GC配置 - 使用ZGC（Java 17+推荐）
    JVM_ARGS="${JVM_ARGS} -XX:+UseZGC"
    JVM_ARGS="${JVM_ARGS} -XX:+UnlockExperimentalVMOptions"
    
    # 系统属性
    JVM_ARGS="${JVM_ARGS} -Djava.security.egd=file:/dev/./urandom"
    JVM_ARGS="${JVM_ARGS} -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE}"
    JVM_ARGS="${JVM_ARGS} -Dfile.encoding=UTF-8"
    JVM_ARGS="${JVM_ARGS} -Duser.timezone=Asia/Shanghai"
    
    # 调试配置（可选）
    if [ "${ENABLE_DEBUG:-false}" = "true" ]; then
        JVM_ARGS="${JVM_ARGS} -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=${DEBUG_PORT:-5005}"
        log_info "启用调试模式，端口: ${DEBUG_PORT:-5005}"
    fi
    
    # JMX配置（可选）
    if [ "${ENABLE_JMX:-false}" = "true" ]; then
        JVM_ARGS="${JVM_ARGS} -Dcom.sun.management.jmxremote"
        JVM_ARGS="${JVM_ARGS} -Dcom.sun.management.jmxremote.port=${JMX_PORT:-9999}"
        JVM_ARGS="${JVM_ARGS} -Dcom.sun.management.jmxremote.authenticate=false"
        JVM_ARGS="${JVM_ARGS} -Dcom.sun.management.jmxremote.ssl=false"
        log_info "启用JMX监控，端口: ${JMX_PORT:-9999}"
    fi
    
    log_debug "JVM参数: ${JVM_ARGS}"
}

# 检查进程是否运行
is_running() {
    if [ -f "${PID_FILE}" ]; then
        local pid=$(cat "${PID_FILE}")
        if ps -p "${pid}" > /dev/null 2>&1; then
            return 0
        else
            rm -f "${PID_FILE}"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start_app() {
    if is_running; then
        log_warn "${SCRIPT_NAME} 已经在运行中，PID: $(cat ${PID_FILE})"
        return 1
    fi
    
    log_info "正在启动 ${SCRIPT_NAME}..."
    
    # 切换到bin目录
    cd "${SCRIPT_DIR}/app/bin" || {
        log_error "无法切换到bin目录"
        exit 1
    }
    
    # 启动应用
    nohup java ${JVM_ARGS} -jar "${JAR_NAME}" > "${LOG_FILE}" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "${pid}" > "${PID_FILE}"
    
    # 等待启动
    sleep 3
    
    if is_running; then
        log_info "${SCRIPT_NAME} 启动成功，PID: ${pid}"
        log_info "日志文件: ${LOG_FILE}"
        log_info "服务地址: http://localhost:${SERVER_PORT}"
        return 0
    else
        log_error "${SCRIPT_NAME} 启动失败"
        return 1
    fi
}

# 停止应用
stop_app() {
    if ! is_running; then
        log_warn "${SCRIPT_NAME} 未运行"
        return 1
    fi
    
    local pid=$(cat "${PID_FILE}")
    log_info "正在停止 ${SCRIPT_NAME}，PID: ${pid}"
    
    # 优雅停止
    kill "${pid}"
    
    # 等待进程结束
    local count=0
    while [ ${count} -lt 30 ] && ps -p "${pid}" > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p "${pid}" > /dev/null 2>&1; then
        log_warn "优雅停止失败，强制终止进程"
        kill -9 "${pid}"
        sleep 2
    fi
    
    rm -f "${PID_FILE}"
    log_info "${SCRIPT_NAME} 已停止"
}

# 重启应用
restart_app() {
    log_info "正在重启 ${SCRIPT_NAME}..."
    stop_app
    sleep 2
    start_app
}

# 查看状态
status_app() {
    if is_running; then
        local pid=$(cat "${PID_FILE}")
        log_info "${SCRIPT_NAME} 正在运行，PID: ${pid}"
        
        # 显示进程信息
        ps -p "${pid}" -o pid,ppid,pcpu,pmem,etime,cmd
        
        # 显示端口占用
        if command -v netstat &> /dev/null; then
            log_info "端口占用情况:"
            netstat -tlnp | grep ":${SERVER_PORT} "
        fi
    else
        log_info "${SCRIPT_NAME} 未运行"
    fi
}

# 查看日志
tail_log() {
    if [ -f "${LOG_FILE}" ]; then
        tail -f "${LOG_FILE}"
    else
        log_error "日志文件不存在: ${LOG_FILE}"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status|log|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动应用"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  status  - 查看运行状态"
    echo "  log     - 查看实时日志"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "环境变量配置文件: ${ENV_FILE}"
    echo "支持格式: REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7"
}

# 主函数
main() {
    create_directories
    check_java
    check_app_structure
    load_environment
    build_jvm_args
    
    case "${1:-help}" in
        start)
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            restart_app
            ;;
        status)
            status_app
            ;;
        log)
            tail_log
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
