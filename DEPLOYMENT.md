# ContiNew Admin Linux 部署指南

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装 Java 17
sudo yum install -y java-17-openjdk java-17-openjdk-devel
# 或者 Ubuntu/Debian
sudo apt-get install -y openjdk-17-jdk

# 验证 Java 版本
java -version
```

### 2. 应用部署

```bash
# 创建应用目录
sudo mkdir -p /opt/continew-admin
sudo chown -R $USER:$USER /opt/continew-admin

# 上传应用文件
cd /opt/continew-admin
# 将打包后的 target/app 目录上传到此处
# 将 start.sh 脚本上传到此处

# 设置执行权限
chmod +x start.sh

# 复制环境变量配置文件
cp .env.example .env
```

### 3. 配置环境变量

编辑 `.env` 文件：

```bash
vim .env
```

**支持两种格式：**

**格式1：标准格式（每行一个变量）**
```bash
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PWD=lizh_2024
REDIS_DB=7
```

**格式2：分号分隔格式（你要求的格式）**
```bash
REDIS_HOST=127.0.0.1;REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7
```

### 4. 启动应用

```bash
# 启动应用
./start.sh start

# 查看状态
./start.sh status

# 查看日志
./start.sh log

# 停止应用
./start.sh stop

# 重启应用
./start.sh restart
```

## 🔧 系统服务配置

### 1. 创建系统用户

```bash
# 创建专用用户
sudo useradd -r -s /bin/false continew
sudo chown -R continew:continew /opt/continew-admin
```

### 2. 安装系统服务

```bash
# 复制服务文件
sudo cp continew-admin.service /etc/systemd/system/

# 重载系统服务
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable continew-admin

# 启动服务
sudo systemctl start continew-admin

# 查看状态
sudo systemctl status continew-admin
```

### 3. 服务管理命令

```bash
# 启动服务
sudo systemctl start continew-admin

# 停止服务
sudo systemctl stop continew-admin

# 重启服务
sudo systemctl restart continew-admin

# 查看日志
sudo journalctl -u continew-admin -f
```

## 📁 目录结构

```
/opt/continew-admin/
├── app/                    # 应用文件
│   ├── bin/               # 主程序JAR
│   │   └── continew-admin.jar
│   ├── lib/               # 依赖JAR
│   │   ├── spring-boot-*.jar
│   │   └── ...
│   └── config/            # 配置文件
│       ├── application.yml
│       └── application-prod.yml
├── logs/                  # 日志目录
│   ├── startup.log        # 启动日志
│   └── continew-admin.log # 应用日志
├── data/                  # 数据目录
│   └── file/             # 文件存储
├── start.sh              # 启动脚本
├── .env                  # 环境变量配置
├── continew-admin.pid    # 进程ID文件
└── continew-admin.service # 系统服务配置
```

## 🔍 故障排查

### 1. 启动失败

```bash
# 检查Java环境
java -version

# 检查目录结构
ls -la app/

# 查看启动日志
tail -f logs/startup.log
```

### 2. 端口占用

```bash
# 检查端口占用
netstat -tlnp | grep :18000
lsof -i :18000

# 杀死占用进程
kill -9 <PID>
```

### 3. 权限问题

```bash
# 检查文件权限
ls -la start.sh

# 设置执行权限
chmod +x start.sh

# 检查目录权限
sudo chown -R continew:continew /opt/continew-admin
```

## ⚡ 性能优化

### 1. JVM参数调优

在 `.env` 文件中配置：

```bash
# 内存配置（根据服务器配置调整）
JVM_XMS=1g
JVM_XMX=4g
JVM_XMN=512m

# 启用调试（开发环境）
ENABLE_DEBUG=true
DEBUG_PORT=5005

# 启用JMX监控
ENABLE_JMX=true
JMX_PORT=9999
```

### 2. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 🔐 安全配置

### 1. 防火墙配置

```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=18000/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian
sudo ufw allow 18000/tcp
```

### 2. SSL证书配置

在 `application-prod.yml` 中配置HTTPS：

```yaml
server:
  port: 443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: your-password
    key-store-type: PKCS12
```

## 📊 监控配置

### 1. 日志监控

```bash
# 使用logrotate管理日志
sudo vim /etc/logrotate.d/continew-admin
```

### 2. 健康检查

```bash
# 创建健康检查脚本
curl -f http://localhost:18000/actuator/health || exit 1
```

## 🔄 自动化部署

### 1. 部署脚本示例

```bash
#!/bin/bash
# deploy.sh

# 停止服务
sudo systemctl stop continew-admin

# 备份当前版本
cp -r /opt/continew-admin/app /opt/continew-admin/app.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
tar -xzf continew-admin-latest.tar.gz -C /opt/continew-admin/

# 启动服务
sudo systemctl start continew-admin

# 检查状态
sleep 10
sudo systemctl status continew-admin
```

### 2. 回滚脚本

```bash
#!/bin/bash
# rollback.sh

BACKUP_DIR=$(ls -t /opt/continew-admin/app.backup.* | head -n1)
sudo systemctl stop continew-admin
rm -rf /opt/continew-admin/app
mv $BACKUP_DIR /opt/continew-admin/app
sudo systemctl start continew-admin
```

---

**🎉 部署完成！访问 http://your-server:18000 查看应用**
