[Unit]
Description=ContiNew Admin Application
Documentation=https://continew.top
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=forking
User=continew
Group=continew
WorkingDirectory=/opt/continew-admin
ExecStart=/opt/continew-admin/start.sh start
ExecStop=/opt/continew-admin/start.sh stop
ExecReload=/opt/continew-admin/start.sh restart
PIDFile=/opt/continew-admin/continew-admin.pid
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=continew-admin

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/continew-admin/logs /opt/continew-admin/data

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-17-openjdk
Environment=PATH=/usr/lib/jvm/java-17-openjdk/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

[Install]
WantedBy=multi-user.target
