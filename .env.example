# ContiNew Admin 环境变量配置文件
# 支持两种格式：
# 1. 标准格式：每行一个变量
# 2. 分号分隔格式：REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7

#=============================================================================
# 应用基础配置
#=============================================================================
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=18000

#=============================================================================
# 数据库配置
#=============================================================================
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=continew_admin
DB_USER=root
DB_PWD=123456

#=============================================================================
# Redis配置 - 支持分号分隔格式
#=============================================================================
REDIS_HOST=127.0.0.1;REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7

#=============================================================================
# 任务调度配置
#=============================================================================
SCHEDULE_HOST=127.0.0.1
SCHEDULE_PORT=1788
SCHEDULE_NAMESPACE=764d604ec6fc45f68cd92514c40e9e1a
SCHEDULE_GROUP=continew-admin
SCHEDULE_TOKEN=SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj
SCHEDULE_USERNAME=admin
SCHEDULE_PASSWORD=admin

#=============================================================================
# JVM配置
#=============================================================================
JVM_XMS=512m
JVM_XMX=2g
JVM_XMN=256m

#=============================================================================
# 调试配置（可选）
#=============================================================================
# ENABLE_DEBUG=true
# DEBUG_PORT=5005

#=============================================================================
# JMX监控配置（可选）
#=============================================================================
# ENABLE_JMX=true
# JMX_PORT=9999

#=============================================================================
# 项目配置
#=============================================================================
PROJECT_URL=https://e.jcengine.com/

#=============================================================================
# 安全配置
#=============================================================================
CRYPTO_PASSWORD=abcdefghijklmnop
CRYPTO_PUBLIC_KEY=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM51dgYtMyF+tTQt80sfFOpSV27a7t9uaUVeFrdGiVxscuizE7H8SMntYqfn9lp8a5GH5P1/GGehVjUD2gF/4kcCAwEAAQ==
CRYPTO_PRIVATE_KEY=MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAznV2Bi0zIX61NC3zSx8U6lJXbtru325pRV4Wt0aJXGxy6LMTsfxIye1ip+f2WnxrkYfk/X8YZ6FWNQPaAX/iRwIDAQABAkEAk/VcAusrpIqA5Ac2P5Tj0VX3cOuXmyouaVcXonr7f+6y2YTjLQuAnkcfKKocQI/juIRQBFQIqqW/m1nmz1wGeQIhAO8XaA/KxzOIgU0l/4lm0A2Wne6RokJ9HLs1YpOzIUmVAiEA3Q9DQrpAlIuiT1yWAGSxA9RxcjUM/1kdVLTkv0avXWsCIE0X8woEjK7lOSwzMG6RpEx9YHdopjViOj1zPVH61KTxAiBmv/dlhqkJ4rV46fIXELZur0pj6WC3N7a4brR8a+CLLQIhAMQyerWl2cPNVtE/8tkziHKbwW3ZUiBXU24wFxedT9iV

#=============================================================================
# 第三方服务配置
#=============================================================================
# 邮件配置
# MAIL_HOST=smtp.126.com
# MAIL_PORT=465
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-auth-code

# 短信配置
# SMS_ACCESS_KEY=your-access-key
# SMS_SECRET_KEY=your-secret-key
# SMS_APP_ID=your-app-id

# 社交登录配置
GITEE_CLIENT_ID=5d271b7f638941812aaf8bfc2e2f08f06d6235ef934e0e39537e2364eb8452c4
GITEE_CLIENT_SECRET=1f7d08**********5b7**********29e
GITHUB_CLIENT_ID=38080dad08cfbdfacca9
GITHUB_CLIENT_SECRET=1f7d08**********5b7**********29e
