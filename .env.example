# ContiNew Admin 环境变量配置文件

# JDK和应用路径配置（可选，不配置则使用默认值）
JAVA_HOME=/home/<USER>/dragonwell-*********.15+7-GA
APP_HOME=/home/<USER>/seeNow/admin
JAR_PATH=/home/<USER>/seeNow/admin/bin/continew-admin.jar

# 应用配置
SPRING_PROFILES_ACTIVE=prod

# 数据库配置
DB_HOST=rm-bp10x95u81d546wfdlo.mysql.rds.aliyuncs.com;DB_PORT=3306;DB_USER=seenow;DB_PWD=seenow;DB_NAME=see-now;

# Redis配置
REDIS_HOST=**************;REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7

# 任务调度配置
SCHEDULE_HOST=127.0.0.1;SCHEDULE_PORT=1788;SCHEDULE_TOKEN=SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj

