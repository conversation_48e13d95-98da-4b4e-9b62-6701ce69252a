# ContiNew Admin 启动脚本

简单实用的Linux启动脚本，支持环境变量配置和基本进程管理。

## 使用方法

### 1. 准备工作

```bash
# 确保Java 17+已安装
java -version

# 设置脚本执行权限
chmod +x start.sh

# 复制环境变量配置文件
cp .env.example .env
```

### 2. 配置JDK和应用路径

编辑 `.env` 文件，配置JDK和应用路径：

```bash
# JDK路径配置（可选）
JAVA_HOME=/usr/lib/jvm/java-17-openjdk

# 应用路径配置（可选）
APP_HOME=/opt/continew-admin
JAR_PATH=/opt/continew-admin/app/bin/continew-admin.jar

# 应用配置
SPRING_PROFILES_ACTIVE=prod

# 数据库配置
DB_HOST=127.0.0.1;DB_PORT=3306;DB_NAME=continew_admin;DB_USER=root;DB_PWD=123456

# Redis配置
REDIS_HOST=127.0.0.1;REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7
```

**路径配置说明：**

1. **JAVA_HOME** - JDK安装路径（可选）
   - 不配置：使用系统PATH中的java命令
   - 配置：使用指定JDK路径的java命令

2. **APP_HOME** - 应用根目录（可选）
   - 不配置：使用脚本所在目录
   - 配置：使用指定的应用目录

3. **JAR_PATH** - JAR文件完整路径（可选）
   - 不配置：使用 `${APP_HOME}/app/bin/continew-admin.jar`
   - 配置：使用指定的JAR文件路径

### 3. 启动应用

```bash
# 启动应用
./start.sh start

# 查看状态
./start.sh status

# 停止应用
./start.sh stop

# 重启应用
./start.sh restart
```

## 配置方式

### 方式1：通过.env文件配置（推荐）

```bash
# 编辑.env文件
vim .env

# 配置JDK路径
JAVA_HOME=/usr/lib/jvm/java-17-openjdk

# 配置应用路径
JAR_PATH=/opt/continew-admin/app/bin/continew-admin.jar
```

### 方式2：通过环境变量配置

```bash
# 设置环境变量后启动
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
export JAR_PATH=/opt/continew-admin/app/bin/continew-admin.jar
./start.sh start
```

### 方式3：直接修改脚本

编辑 `start.sh` 文件，修改以下行：

```bash
# 在脚本开头修改默认路径
JAVA_HOME="/usr/lib/jvm/java-17-openjdk"
APP_HOME="/opt/continew-admin"
JAR_PATH="/opt/continew-admin/app/bin/continew-admin.jar"
```

## 目录结构

```
project/
├── app/                    # 应用文件（mvn package生成）
│   ├── bin/               # 主程序JAR
│   ├── lib/               # 依赖JAR
│   └── config/            # 配置文件
├── start.sh               # 启动脚本
├── .env                   # 环境变量配置
├── continew-admin.pid     # 进程ID文件（自动生成）
└── README.md              # 使用说明
```

## 命令说明

- `start` - 启动应用
- `stop` - 停止应用  
- `restart` - 重启应用
- `status` - 查看运行状态

## 注意事项

1. 确保 `app/bin/continew-admin.jar` 文件存在
2. 环境变量支持分号分隔格式：`KEY1=value1;KEY2=value2`
3. 应用启动后会在后台运行，PID保存在 `continew-admin.pid` 文件中
