# ContiNew Admin 启动脚本

简单实用的Linux启动脚本，支持环境变量配置和基本进程管理。

## 使用方法

### 1. 准备工作

```bash
# 确保Java 17+已安装
java -version

# 设置脚本执行权限
chmod +x start.sh

# 复制环境变量配置文件
cp .env.example .env
```

### 2. 配置环境变量

编辑 `.env` 文件，支持分号分隔格式：

```bash
# 应用配置
SPRING_PROFILES_ACTIVE=prod

# 数据库配置
DB_HOST=127.0.0.1;DB_PORT=3306;DB_NAME=continew_admin;DB_USER=root;DB_PWD=123456

# Redis配置
REDIS_HOST=127.0.0.1;REDIS_PORT=6379;REDIS_PWD=lizh_2024;REDIS_DB=7

# 任务调度配置
SCHEDULE_HOST=127.0.0.1;SCHEDULE_PORT=1788;SCHEDULE_TOKEN=your-token
```

### 3. 启动应用

```bash
# 启动应用
./start.sh start

# 查看状态
./start.sh status

# 停止应用
./start.sh stop

# 重启应用
./start.sh restart
```

## 目录结构

```
project/
├── app/                    # 应用文件（mvn package生成）
│   ├── bin/               # 主程序JAR
│   ├── lib/               # 依赖JAR
│   └── config/            # 配置文件
├── start.sh               # 启动脚本
├── .env                   # 环境变量配置
├── continew-admin.pid     # 进程ID文件（自动生成）
└── README.md              # 使用说明
```

## 命令说明

- `start` - 启动应用
- `stop` - 停止应用  
- `restart` - 重启应用
- `status` - 查看运行状态

## 注意事项

1. 确保 `app/bin/continew-admin.jar` 文件存在
2. 环境变量支持分号分隔格式：`KEY1=value1;KEY2=value2`
3. 应用启动后会在后台运行，PID保存在 `continew-admin.pid` 文件中
