#!/bin/bash

#=============================================================================
# 快速启动脚本 - 直接配置路径，不依赖复杂逻辑
#=============================================================================

# 直接配置你的路径（根据你的实际情况修改这里）
JAVA_HOME="/home/<USER>/dragonwell-17.0.14.0.15+7-GA"
APP_HOME="/home/<USER>/seeNow/admin"
JAR_NAME="continew-admin.jar"
JAR_PATH="${APP_HOME}/bin/${JAR_NAME}"
PID_FILE="${APP_HOME}/continew-admin.pid"

# 检查并设置Java命令
if [ -n "${JAVA_HOME}" ] && [ -x "${JAVA_HOME}/bin/java" ]; then
    JAVA_CMD="${JAVA_HOME}/bin/java"
    echo "使用指定的Java: ${JAVA_CMD}"
else
    JAVA_CMD="java"
    echo "使用系统Java: $(which java)"
fi

# 显示Java版本
echo "Java版本: $(${JAVA_CMD} -version 2>&1 | head -n1)"

# 检查JAR文件
echo "检查JAR文件: ${JAR_PATH}"
if [ ! -f "${JAR_PATH}" ]; then
    echo "错误: 找不到JAR文件: ${JAR_PATH}"
    echo "请检查以下内容："
    echo "1. JAR文件是否存在"
    echo "2. 路径是否正确"
    echo "3. 是否已执行 mvn clean package"
    exit 1
fi
echo "JAR文件检查通过"

# 加载环境变量（如果.env文件存在）
ENV_FILE="${APP_HOME}/.env"
if [ -f "${ENV_FILE}" ]; then
    echo "加载环境变量: ${ENV_FILE}"
    while IFS= read -r line || [ -n "$line" ]; do
        [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue
        IFS=';' read -ra VARS <<< "$line"
        for var in "${VARS[@]}"; do
            var=$(echo "$var" | xargs)
            if [[ "$var" =~ ^[A-Za-z_][A-Za-z0-9_]*= ]]; then
                export "$var"
            fi
        done
    done < "${ENV_FILE}"
fi

# 设置默认Profile
export SPRING_PROFILES_ACTIVE="${SPRING_PROFILES_ACTIVE:-prod}"

# 检查进程是否运行
is_running() {
    if [ -f "${PID_FILE}" ]; then
        local pid=$(cat "${PID_FILE}")
        if ps -p "${pid}" > /dev/null 2>&1; then
            return 0
        else
            rm -f "${PID_FILE}"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start_app() {
    if is_running; then
        echo "应用已经在运行中，PID: $(cat ${PID_FILE})"
        return 1
    fi
    
    echo "正在启动应用..."
    echo "使用Java: ${JAVA_CMD}"
    echo "JAR文件: ${JAR_PATH}"
    echo "Profile: ${SPRING_PROFILES_ACTIVE}"
    
    # 切换到JAR文件所在目录
    JAR_DIR="$(dirname "${JAR_PATH}")"
    cd "${JAR_DIR}" || {
        echo "错误: 无法切换到目录: ${JAR_DIR}"
        exit 1
    }
    
    # 启动应用
    nohup ${JAVA_CMD} -jar "$(basename "${JAR_PATH}")" --spring.profiles.active=${SPRING_PROFILES_ACTIVE} > /dev/null 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "${pid}" > "${PID_FILE}"
    
    # 等待启动
    sleep 3
    
    if is_running; then
        echo "应用启动成功，PID: ${pid}"
        return 0
    else
        echo "应用启动失败"
        return 1
    fi
}

# 停止应用
stop_app() {
    if ! is_running; then
        echo "应用未运行"
        return 1
    fi
    
    local pid=$(cat "${PID_FILE}")
    echo "正在停止应用，PID: ${pid}"
    
    kill "${pid}"
    
    local count=0
    while [ ${count} -lt 10 ] && ps -p "${pid}" > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p "${pid}" > /dev/null 2>&1; then
        echo "强制终止进程"
        kill -9 "${pid}"
    fi
    
    rm -f "${PID_FILE}"
    echo "应用已停止"
}

# 查看状态
status_app() {
    if is_running; then
        local pid=$(cat "${PID_FILE}")
        echo "应用正在运行，PID: ${pid}"
    else
        echo "应用未运行"
    fi
}

# 主函数
case "${1:-help}" in
    start)
        start_app
        ;;
    stop)
        stop_app
        ;;
    restart)
        stop_app
        sleep 2
        start_app
        ;;
    status)
        status_app
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "当前配置:"
        echo "  JAVA_HOME: ${JAVA_HOME}"
        echo "  APP_HOME: ${APP_HOME}"
        echo "  JAR_PATH: ${JAR_PATH}"
        ;;
esac
